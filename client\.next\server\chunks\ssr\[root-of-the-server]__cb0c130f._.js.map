{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notificaiton/client/app/page.js"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <>\n      <Link href=\"/user\">user</Link>\n      <Link href=\"/admin\">admin</Link>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,uKAAI;gBAAC,MAAK;0BAAQ;;;;;;0BACnB,8OAAC,uKAAI;gBAAC,MAAK;0BAAS;;;;;;;;AAG1B", "debugId": null}}]}