{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notificaiton/client/app/page.js"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"flex flex-col justify-center gap-4\">\n      <Link href=\"/user\">user</Link> <br/>\n      <Link href=\"/admin\">admin</Link>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uKAAI;gBAAC,MAAK;0BAAQ;;;;;;YAAW;0BAAC,8OAAC;;;;;0BAChC,8OAAC,uKAAI;gBAAC,MAAK;0BAAS;;;;;;;;;;;;AAG1B", "debugId": null}}]}